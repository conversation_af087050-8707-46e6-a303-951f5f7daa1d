import React from 'react';
import Head from 'next/head';

export default function Home() {
  return (
    <div className="bg-[#141735] min-h-screen">
      <Head>
        <title>BioAscension – Discover Your Genetic Potential</title>
        <meta name="description" content="Unlock your genetic potential with AI-powered analysis" />
      </Head>
      
      {/* Navigation */}
      <nav className="relative z-50 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="text-2xl font-bold text-white">
            <span className="bg-gradient-to-r from-[#00C2A8] to-[#4ECDC4] text-transparent bg-clip-text">BioAscension</span>
          </div>
          <div className="hidden md:flex items-center space-x-8 text-white">
            <a href="#" className="hover:text-[#00C2A8] transition-colors">How It Works</a>
            <a href="#" className="hover:text-[#00C2A8] transition-colors">Pricing</a>
            <a href="#" className="hover:text-[#00C2A8] transition-colors">FAQ</a>
            <a href="#" className="hover:text-[#00C2A8] transition-colors">Contact</a>
          </div>
          <button className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-6 py-2 rounded-lg font-semibold hover:shadow-lg hover:shadow-[#00C2A8]/25 transition-all">
            Get Started
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-[#0A0E3F] via-[#141735] to-[#1a1f4a]"></div>
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%2300C2A8\" fill-opacity=\"0.02\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"1\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 max-w-7xl mx-auto px-6 py-20">
          <div className="text-center max-w-4xl mx-auto">
            
            {/* Badge */}
            <div className="inline-flex items-center bg-gradient-to-r from-[#00C2A8]/10 to-[#0A0E3F]/10 border border-[#00C2A8]/20 rounded-full px-6 py-2 mb-8 backdrop-blur-sm">
              <div className="w-2 h-2 bg-[#00C2A8] rounded-full mr-3 animate-pulse"></div>
              <span className="text-[#00C2A8] text-sm font-medium tracking-wide uppercase">World's First AI-Powered Genetic Analysis</span>
            </div>

            {/* Main Headline */}
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-black mb-8 leading-[0.9] tracking-tight">
              <span className="text-white">Unlock Your</span>
              <br />
              <span className="bg-gradient-to-r from-[#00C2A8] via-[#4ECDC4] to-[#00C2A8] text-transparent bg-clip-text animate-pulse">Genetic</span>
              <br />
              <span className="text-white">Potential</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Discover what your genetics are truly capable of. Get personalized insights on your physical development, optimization strategies, and genetic advantages with our revolutionary AI technology.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              <button className="group relative bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-12 py-4 rounded-2xl text-lg font-bold shadow-2xl hover:shadow-[#00C2A8]/30 transition-all duration-300 hover:scale-105 hover:-translate-y-1">
                <span className="relative z-10 flex items-center">
                  Start Your Analysis
                  <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] rounded-2xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
              </button>
              
              <button className="group border-2 border-gray-600 text-white px-12 py-4 rounded-2xl text-lg font-semibold hover:border-[#00C2A8] hover:text-[#00C2A8] hover:shadow-lg hover:shadow-[#00C2A8]/20 transition-all duration-300">
                Watch Demo
                <svg className="w-5 h-5 ml-2 inline group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-12 text-sm text-gray-400 mb-20">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-[#00C2A8] to-[#4ECDC4] rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-medium">95% Accuracy Rate</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-[#00C2A8] to-[#4ECDC4] rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-medium">10,000+ Profiles Analyzed</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-[#00C2A8] to-[#4ECDC4] rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-medium">Science-Backed Results</span>
              </div>
            </div>

            {/* Visual Element */}
            <div className="relative max-w-2xl mx-auto">
              <div className="relative">
                {/* Central DNA Visual */}
                <div className="w-32 h-32 mx-auto bg-gradient-to-br from-[#00C2A8]/20 to-[#0A0E3F]/20 rounded-full backdrop-blur-sm border border-[#00C2A8]/30 flex items-center justify-center shadow-2xl">
                  <div className="text-6xl animate-pulse">🧬</div>
                </div>
                
                {/* Floating Stats */}
                <div className="absolute -top-4 -left-16 bg-[#0A0E3F]/80 backdrop-blur-sm border border-[#00C2A8]/30 rounded-xl px-4 py-2 animate-bounce">
                  <div className="text-lg font-bold text-[#00C2A8]">95%</div>
                  <div className="text-xs text-gray-300">Accuracy</div>
                </div>
                
                <div className="absolute -bottom-4 -right-16 bg-[#0A0E3F]/80 backdrop-blur-sm border border-[#00C2A8]/30 rounded-xl px-4 py-2 animate-pulse">
                  <div className="text-lg font-bold text-[#00C2A8]">AI</div>
                  <div className="text-xs text-gray-300">Powered</div>
                </div>
                
                <div className="absolute top-1/2 -right-20 bg-[#0A0E3F]/80 backdrop-blur-sm border border-[#00C2A8]/30 rounded-xl px-4 py-2 animate-ping">
                  <div className="text-lg font-bold text-[#00C2A8]">10K+</div>
                  <div className="text-xs text-gray-300">Users</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Fade */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#141735] to-transparent"></div>
      </section>

      {/* Placeholder for other sections */}
      <div className="bg-[#141735] text-white text-center py-20">
        <h2 className="text-3xl font-bold mb-4">Professional Hero Section Complete!</h2>
        <p className="text-gray-300">Ready for the next sections...</p>
      </div>
    </div>
  )
}
