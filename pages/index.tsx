import Head from 'next/head';
import { useState } from 'react';

export default function Home() {
  const [openFaq, setOpenFaq] = useState<number | null>(null);
  const faqs = [
    {
      question: 'Is this accurate without a DNA test?',
      answer: 'Yes — we trained on thousands of data points. Our accuracy is >95%.'
    },
    {
      question: 'Do I need a doctor?',
      answer: 'Nope. You just answer questions, our systems deliver.'
    },
    {
      question: 'Can this help with looksmaxing?',
      answer: 'Yes. It helps you know if your growth plates are open, how much potential you have left, and what areas to improve.'
    }
  ];

  return (
    <div className="bg-[#141735] min-h-screen flex flex-col">
      <Head>
        <title>BioAscension – Discover Your Genetic Potential</title>
        <meta name="description" content="Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of." />
      </Head>
      {/* Navbar */}
      <header className="sticky top-0 z-30 bg-[#141735] text-white shadow flex items-center justify-between px-8 py-4">
        <div className="font-extrabold text-2xl tracking-tight flex items-center gap-2">
          <span className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-transparent bg-clip-text">BioAscension</span>
        </div>
        <nav className="hidden md:flex gap-10 text-base font-semibold mx-auto">
          <a href="#how" className="hover:text-teal-400 transition">How It Works</a>
          <a href="#pricing" className="hover:text-teal-400 transition">Pricing</a>
          <a href="#faq" className="hover:text-teal-400 transition">FAQ</a>
          <a href="#contact" className="hover:text-teal-400 transition">Contact</a>
        </nav>
        <a href="#quiz" className="bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-6 py-2 rounded-lg shadow font-bold hover:from-teal-500 hover:to-blue-900 transition">Start Quiz</a>
      </header>

      {/* Hero Section */}
      <section className="flex justify-center items-center pt-0 pb-16 px-2 md:px-0 min-h-[60vh]">
        <div className="w-full max-w-[98vw] md:max-w-[90vw] flex flex-col md:flex-row items-center justify-between">
          <div className="flex-1 flex flex-col justify-center items-start bg-[#0A0E3F] rounded-3xl shadow-2xl p-6 md:p-12 text-white relative z-10 min-w-0">
            <h1 className="text-4xl md:text-5xl font-extrabold mb-4 leading-tight">Discover Your Genetic Potential</h1>
            <p className="text-lg md:text-xl mb-6 max-w-lg text-blue-100">Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of.</p>
            <a href="#quiz" className="inline-block bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-8 py-3 rounded-lg shadow-lg text-lg font-bold hover:from-teal-500 hover:to-blue-900 transition">Start Your Quiz</a>
            <p className="mt-4 text-sm text-blue-200">The world's first instant, science-based puberty and growth prediction tool.</p>
          </div>
          <div className="flex-1 flex justify-center items-center mt-10 md:mt-0 md:ml-[-60px] relative z-0 min-w-0">
            {/* Placeholder for 3D DNA/human visual */}
            <div className="w-72 h-72 bg-gradient-to-tr from-[#00C2A8] to-[#0A0E3F] rounded-3xl flex items-center justify-center shadow-2xl border-8 border-[#112266]">
              <span className="text-7xl opacity-70">🧬</span>
            </div>
          </div>
        </div>
      </section>

      {/* Why BioAscension */}
      <section className="py-16 px-6 md:px-20" id="why" style={{ backgroundColor: '#141735' }}>
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-10" style={{ color: '#00C2A8' }}>Why BioAscension?</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
          <div className="bg-gray-50 rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">⚡</span>
            <h3 className="font-semibold mb-1">Instant Results</h3>
            <p className="text-sm text-gray-600 text-center">No waiting, no labs</p>
          </div>
          <div className="bg-gray-50 rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">🔬</span>
            <h3 className="font-semibold mb-1">95% Accuracy</h3>
            <p className="text-sm text-gray-600 text-center">Based on real science, trained on real data</p>
          </div>
          <div className="bg-gray-50 rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">💸</span>
            <h3 className="font-semibold mb-1">Affordable</h3>
            <p className="text-sm text-gray-600 text-center">From only $1.00</p>
          </div>
          <div className="bg-gray-50 rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">🧑‍🔬</span>
            <h3 className="font-semibold mb-1">Looksmax Tool</h3>
            <p className="text-sm text-gray-600 text-center">Perfect for teens and young adults</p>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 px-6 md:px-20" id="how" style={{ backgroundColor: '#141735' }}>
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-10" style={{ color: '#00C2A8' }}>How It Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">📝</span>
            <h3 className="font-semibold mb-1">Take the Quiz</h3>
            <p className="text-sm text-gray-600 text-center">10–20 mins, simple questions</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">📊</span>
            <h3 className="font-semibold mb-1">We Analyze</h3>
            <p className="text-sm text-gray-600 text-center">Growth, hormone, and puberty traits</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center">
            <span className="text-3xl mb-2">⚡</span>
            <h3 className="font-semibold mb-1">Get Results Instantly</h3>
            <p className="text-sm text-gray-600 text-center">No lab, no wait. Results sent to Gmail.</p>
          </div>
        </div>
      </section>

      {/* Sample Predictions Dashboard */}
      <section className="py-16 px-6 md:px-20" id="dashboard" style={{ backgroundColor: '#141735' }}>
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-4" style={{ color: '#00C2A8' }}>See Your Potential</h2>
        <p className="text-center text-gray-300 mb-12 max-w-2xl mx-auto">Get detailed insights into your genetic potential with our advanced prediction models</p>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12">
          {/* Height Growth Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">📏</div>
              <h3 className="font-bold mb-3 text-center">Height Growth Range</h3>

              {/* Enhanced Progress Bar with Animation */}
              <div className="w-full mb-4">
                <div className="flex justify-between text-xs mb-1">
                  <span>5'8"</span>
                  <span>6'2"</span>
                </div>
                <div className="w-full h-3 bg-white bg-opacity-20 rounded-full overflow-hidden">
                  <div className="h-3 bg-gradient-to-r from-white to-teal-200 rounded-full animate-pulse" style={{ width: '70%' }}></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-sm font-semibold">Current: 5'10"</span>
                </div>
              </div>

              {/* Growth Chart Visualization */}
              <div className="w-full h-12 flex items-end justify-center space-x-1">
                <div className="w-2 h-4 bg-white bg-opacity-60 rounded-t"></div>
                <div className="w-2 h-6 bg-white bg-opacity-70 rounded-t"></div>
                <div className="w-2 h-8 bg-white bg-opacity-80 rounded-t"></div>
                <div className="w-2 h-10 bg-white rounded-t"></div>
                <div className="w-2 h-12 bg-teal-200 rounded-t animate-bounce"></div>
              </div>
            </div>
          </div>

          {/* Testosterone Trajectory Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute top-0 left-0 w-16 h-16 bg-white bg-opacity-10 rounded-full -ml-8 -mt-8"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">🧪</div>
              <h3 className="font-bold mb-3 text-center">Testosterone Trajectory</h3>

              {/* Enhanced Bar Chart */}
              <div className="w-full h-20 flex items-end justify-center space-x-2 mb-3">
                <div className="flex flex-col items-center">
                  <div className="w-3 h-8 bg-white bg-opacity-60 rounded-t mb-1"></div>
                  <span className="text-xs">16</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-12 bg-white bg-opacity-70 rounded-t mb-1"></div>
                  <span className="text-xs">17</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-16 bg-white bg-opacity-80 rounded-t mb-1"></div>
                  <span className="text-xs">18</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-14 bg-white bg-opacity-90 rounded-t mb-1"></div>
                  <span className="text-xs">19</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="w-3 h-20 bg-teal-200 rounded-t mb-1 animate-pulse"></div>
                  <span className="text-xs font-bold">20</span>
                </div>
              </div>

              <div className="text-center">
                <span className="text-sm font-semibold bg-white bg-opacity-20 px-2 py-1 rounded">📈 Rising Trend</span>
              </div>
            </div>
          </div>

          {/* Facial Bone Maturity Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -mr-12 -mb-12"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">💀</div>
              <h3 className="font-bold mb-3 text-center">Facial Bone Maturity</h3>

              {/* Circular Progress */}
              <div className="relative w-20 h-20 mb-3">
                <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-white text-opacity-20"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-teal-200"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeDasharray="50, 100"
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold">50%</span>
                </div>
              </div>

              <div className="text-center">
                <span className="text-sm font-semibold">Still Growing</span>
                <div className="text-xs mt-1 bg-white bg-opacity-20 px-2 py-1 rounded">Peak at 25</div>
              </div>
            </div>
          </div>

          {/* Puberty Stage Card */}
          <div className="bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden">
            <div className="absolute top-0 right-0 w-32 h-8 bg-white bg-opacity-10 rounded-full -mr-16 -mt-4 transform rotate-45"></div>
            <div className="relative z-10 flex flex-col items-center w-full">
              <div className="text-4xl mb-3">⏳</div>
              <h3 className="font-bold mb-3 text-center">Puberty Stage</h3>

              {/* Stage Indicator */}
              <div className="w-full mb-4">
                <div className="flex justify-between text-xs mb-2">
                  <span>Early</span>
                  <span>Peak</span>
                  <span>Late</span>
                </div>
                <div className="w-full h-3 bg-white bg-opacity-20 rounded-full overflow-hidden">
                  <div className="h-3 bg-gradient-to-r from-white to-teal-200 rounded-full" style={{ width: '80%' }}></div>
                </div>
              </div>

              {/* Timeline Dots */}
              <div className="flex justify-between w-full mb-3">
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <div className="w-3 h-3 bg-teal-200 rounded-full animate-ping"></div>
                <div className="w-3 h-3 bg-white bg-opacity-40 rounded-full"></div>
              </div>

              <div className="text-center">
                <span className="text-sm font-semibold">Late Stage</span>
                <div className="text-xs mt-1">80% Complete</div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Visual Elements */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8 text-white text-center">
            <h3 className="text-xl font-bold mb-4">🎯 Your Genetic Potential Score</h3>
            <div className="flex justify-center items-center space-x-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-200">8.7</div>
                <div className="text-sm">Overall Score</div>
              </div>
              <div className="text-6xl opacity-20">|</div>
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-200">92%</div>
                <div className="text-sm">Accuracy</div>
              </div>
              <div className="text-6xl opacity-20">|</div>
              <div className="text-center">
                <div className="text-3xl font-bold text-teal-200">2.3</div>
                <div className="text-sm">Years Left</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="py-16 px-6 md:px-20" id="pricing" style={{ backgroundColor: '#141735' }}>
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-10" style={{ color: '#00C2A8' }}>Simple, No BS Pricing</h2>
        <div className="flex flex-col md:flex-row gap-8 max-w-3xl mx-auto justify-center">
          <div className="flex-1 bg-white rounded-lg shadow p-8 flex flex-col items-center border-2 border-[#00C2A8]">
            <h3 className="font-semibold text-lg mb-2">One-time Report</h3>
            <div className="text-3xl font-bold mb-2">$1.00</div>
            <ul className="text-sm text-gray-600 mb-4 text-center">
              <li>Instant growth & puberty report</li>
              <li>Sent to your Gmail</li>
            </ul>
            <button className="bg-[#00C2A8] text-white px-6 py-2 rounded shadow hover:bg-teal-500 font-semibold transition">Buy Now</button>
          </div>
          <div className="flex-1 bg-white rounded-lg shadow p-8 flex flex-col items-center border-2 border-[#0A0E3F]">
            <h3 className="font-semibold text-lg mb-2">Maximizing Potential Blueprint</h3>
            <div className="text-3xl font-bold mb-2">$4.99</div>
            <ul className="text-sm text-gray-600 mb-4 text-center">
              <li>Advanced looksmax tips</li>
              <li>Puberty phase timeline</li>
              <li>Teaser of final height/features if maximized</li>
            </ul>
            <button className="bg-[#0A0E3F] text-white px-6 py-2 rounded shadow hover:bg-blue-900 font-semibold transition">Buy Now</button>
          </div>
        </div>
        <div className="flex justify-center mt-6">
          <img src="https://cdn.jsdelivr.net/gh/stripe/stripe-js@v1.32.0/dist/stripe-logo.svg" alt="Stripe" className="h-6" />
        </div>
      </section>

      {/* FAQ */}
      <section className="py-16 px-6 md:px-20" id="faq" style={{ backgroundColor: '#141735' }}>
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-10" style={{ color: '#00C2A8' }}>Frequently Asked Questions</h2>
        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, idx) => (
            <div key={idx} className="bg-gray-50 rounded-lg shadow">
              <button
                className="w-full text-left p-6 focus:outline-none flex justify-between items-center"
                onClick={() => setOpenFaq(openFaq === idx ? null : idx)}
                aria-expanded={openFaq === idx}
                aria-controls={`faq-answer-${idx}`}
              >
                <span className="font-semibold">{faq.question}</span>
                <span className={`ml-4 transition-transform ${openFaq === idx ? 'rotate-180' : ''}`}>▼</span>
              </button>
              {openFaq === idx && (
                <div id={`faq-answer-${idx}`} className="px-6 pb-6 text-sm text-gray-600 animate-fade-in">
                  {faq.answer}
                </div>
              )}
            </div>
          ))}
        </div>
      </section>

      {/* Contact / Support */}
      <section className="py-16 px-6 md:px-20 bg-gray-50" id="contact">
        <h2 className="text-2xl md:text-3xl font-bold text-center mb-6 text-[#0A0E3F]">Need Help?</h2>
        <p className="text-center text-gray-700 mb-4">Email support: <a href="mailto:<EMAIL>" className="text-[#00C2A8] underline"><EMAIL></a></p>
      </section>

      {/* Footer */}
      <footer className="bg-[#0A0E3F] text-white py-8 px-6 md:px-20 mt-auto">
        <div className="flex flex-col md:flex-row justify-between items-center max-w-6xl mx-auto">
          <div className="font-bold text-lg mb-4 md:mb-0">BioAscension</div>
          <div className="flex gap-6 text-sm mb-4 md:mb-0">
            <a href="#" className="hover:text-teal-400">Home</a>
            <a href="#how" className="hover:text-teal-400">How It Works</a>
            <a href="#pricing" className="hover:text-teal-400">Pricing</a>
            <a href="#faq" className="hover:text-teal-400">FAQ</a>
            <a href="#contact" className="hover:text-teal-400">Contact</a>
          </div>
          <div className="text-xs text-gray-300">© 2024 BioAscension. All rights reserved.</div>
        </div>
      </footer>
    </div>
  );
}
