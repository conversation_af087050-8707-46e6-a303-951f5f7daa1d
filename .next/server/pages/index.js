/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/index.tsx */ \"(pages-dir-node)/./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/_app.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/_app.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRCO0FBQ0U7QUFFZixTQUFTQyxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELHFCQUNFOzswQkFDRSw4REFBQ0gsa0RBQUlBOzBCQUNILDRFQUFDSTtvQkFBS0MsTUFBSztvQkFBb0ZDLEtBQUk7Ozs7Ozs7Ozs7OzBCQUVyRyw4REFBQ0o7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9leGFtL0Rlc2t0b3AvcHJvamVjdHMvYmlvYXNjZW5zaW9uL2Jpb2FzY2Vuc2lvbi9wYWdlcy9fYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnXG5pbXBvcnQgJy4uL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPGxpbmsgaHJlZj1cImh0dHBzOi8vZm9udHMuZ29vZ2xlYXBpcy5jb20vY3NzMj9mYW1pbHk9TW9udHNlcnJhdDp3Z2h0QDQwMDs2MDA7NzAwJmRpc3BsYXk9c3dhcFwiIHJlbD1cInN0eWxlc2hlZXRcIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvPlxuICApXG59Il0sIm5hbWVzIjpbIkhlYWQiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJsaW5rIiwiaHJlZiIsInJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Home() {\n    const [openFaq, setOpenFaq] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const faqs = [\n        {\n            question: 'Is this accurate without a DNA test?',\n            answer: 'Yes — we trained on thousands of data points. Our accuracy is >95%.'\n        },\n        {\n            question: 'Do I need a doctor?',\n            answer: 'Nope. You just answer questions, our systems deliver.'\n        },\n        {\n            question: 'Can this help with looksmaxing?',\n            answer: 'Yes. It helps you know if your growth plates are open, how much potential you have left, and what areas to improve.'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#141735] min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"BioAscension – Discover Your Genetic Potential\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"sticky top-0 z-30 bg-[#141735] text-white shadow flex items-center justify-between px-8 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"font-extrabold text-2xl tracking-tight flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-transparent bg-clip-text\",\n                            children: \"BioAscension\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex gap-10 text-base font-semibold mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#how\",\n                                className: \"hover:text-teal-400 transition\",\n                                children: \"How It Works\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#pricing\",\n                                className: \"hover:text-teal-400 transition\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#faq\",\n                                className: \"hover:text-teal-400 transition\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#contact\",\n                                className: \"hover:text-teal-400 transition\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#quiz\",\n                        className: \"bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-6 py-2 rounded-lg shadow font-bold hover:from-teal-500 hover:to-blue-900 transition\",\n                        children: \"Start Quiz\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"flex justify-center items-center pt-0 pb-16 px-2 md:px-0 min-h-[60vh]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-[98vw] md:max-w-[90vw] flex flex-col md:flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex flex-col justify-center items-start bg-[#0A0E3F] rounded-3xl shadow-2xl p-6 md:p-12 text-white relative z-10 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-5xl font-extrabold mb-4 leading-tight\",\n                                    children: \"Discover Your Genetic Potential\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg md:text-xl mb-6 max-w-lg text-blue-100\",\n                                    children: \"Looksmax your face, height, and frame by understanding what your puberty + genetics are truly capable of.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#quiz\",\n                                    className: \"inline-block bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white px-8 py-3 rounded-lg shadow-lg text-lg font-bold hover:from-teal-500 hover:to-blue-900 transition\",\n                                    children: \"Start Your Quiz\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-blue-200\",\n                                    children: \"The world's first instant, science-based puberty and growth prediction tool.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 flex justify-center items-center mt-10 md:mt-0 md:ml-[-60px] relative z-0 min-w-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-72 h-72 bg-gradient-to-tr from-[#00C2A8] to-[#0A0E3F] rounded-3xl flex items-center justify-center shadow-2xl border-8 border-[#112266]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-7xl opacity-70\",\n                                    children: \"\\uD83E\\uDDEC\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-6 md:px-20\",\n                id: \"why\",\n                style: {\n                    backgroundColor: '#141735'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    style: {\n                                        color: '#00C2A8'\n                                    },\n                                    children: \"Why Choose BioAscension?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Advanced genetic analysis powered by cutting-edge AI and validated by thousands of data points\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-3 text-gray-800\",\n                                            children: \"Instant Results\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Get comprehensive genetic analysis in seconds, not weeks\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-[#00C2A8] font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"No lab visits required\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-3 text-gray-800\",\n                                            children: \"95% Accuracy\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Validated against thousands of real genetic profiles and outcomes\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-[#00C2A8] font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Science-backed predictions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-3 text-gray-800\",\n                                            children: \"Affordable Access\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Professional genetic analysis starting from just $1.00\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-[#00C2A8] font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"No hidden fees\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center text-center hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 relative overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-3 text-gray-800\",\n                                            children: \"Optimization Tool\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Maximize your genetic potential with personalized recommendations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-[#00C2A8] font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Tailored for growth phases\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-white text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold mb-2\",\n                                                children: \"10,000+\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Genetic Profiles Analyzed\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold mb-2\",\n                                                children: \"95%\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Prediction Accuracy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-3xl font-bold mb-2\",\n                                                children: \"24/7\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"Expert Support\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-6 md:px-20\",\n                id: \"how\",\n                style: {\n                    backgroundColor: '#141735'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    style: {\n                                        color: '#00C2A8'\n                                    },\n                                    children: \"How It Works\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Our advanced AI analyzes your genetic markers through a simple questionnaire to deliver precise predictions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-[#00C2A8] via-[#00C2A8] to-[#0A0E3F] transform -translate-y-1/2 z-0\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-12 relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group flex flex-col items-center text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 h-24 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-white\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-[#0A0E3F]\",\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-2xl shadow-xl p-8 w-full max-w-sm group-hover:shadow-2xl transition-shadow duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold mb-4 text-gray-800\",\n                                                            children: \"Complete Assessment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6\",\n                                                            children: \"Answer detailed questions about your growth patterns, family history, and physical development\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"10-15 minutes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 187,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 190,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"50+ data points\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 191,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 194,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Privacy protected\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 195,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group flex flex-col items-center text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 h-24 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-white\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-[#0A0E3F]\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-2xl shadow-xl p-8 w-full max-w-sm group-hover:shadow-2xl transition-shadow duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold mb-4 text-gray-800\",\n                                                            children: \"AI Analysis\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6\",\n                                                            children: \"Our advanced algorithms process your data against thousands of genetic profiles and growth patterns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 220,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Machine learning models\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 221,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"10,000+ data points\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 225,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Real-time processing\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 229,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group flex flex-col items-center text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-24 h-24 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-full flex items-center justify-center shadow-2xl group-hover:scale-110 transition-transform duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-12 h-12 text-white\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-bold text-[#0A0E3F]\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-2xl shadow-xl p-8 w-full max-w-sm group-hover:shadow-2xl transition-shadow duration-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold mb-4 text-gray-800\",\n                                                            children: \"Instant Results\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 mb-6\",\n                                                            children: \"Receive comprehensive genetic potential report with actionable insights and recommendations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Detailed PDF report\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Personalized recommendations\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 259,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center text-sm text-gray-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-[#00C2A8] rounded-full mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Email delivery\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                            lineNumber: 263,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-20 bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-white text-center mb-8\",\n                                    children: \"Powered by Advanced Technology\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-6 text-white text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: \"Machine Learning\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: \"Big Data Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: \"Secure Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold\",\n                                                    children: \"Validated Results\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 px-6 md:px-20\",\n                id: \"dashboard\",\n                style: {\n                    backgroundColor: '#141735'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl md:text-3xl font-bold text-center mb-4\",\n                        style: {\n                            color: '#00C2A8'\n                        },\n                        children: \"See Your Potential\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-gray-300 mb-12 max-w-2xl mx-auto\",\n                        children: \"Get detailed insights into your genetic potential with our advanced prediction models\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex flex-col items-center w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-3\",\n                                                children: \"\\uD83D\\uDCCF\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-3 text-center\",\n                                                children: \"Height Growth Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"5'8\\\"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"6'2\\\"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-3 bg-white bg-opacity-20 rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-3 bg-gradient-to-r from-white to-teal-200 rounded-full animate-pulse\",\n                                                            style: {\n                                                                width: '70%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-semibold\",\n                                                            children: \"Current: 5'10\\\"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-12 flex items-end justify-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-4 bg-white bg-opacity-60 rounded-t\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-6 bg-white bg-opacity-70 rounded-t\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-8 bg-white bg-opacity-80 rounded-t\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-10 bg-white rounded-t\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-12 bg-teal-200 rounded-t animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 left-0 w-16 h-16 bg-white bg-opacity-10 rounded-full -ml-8 -mt-8\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex flex-col items-center w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-3\",\n                                                children: \"\\uD83E\\uDDEA\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-3 text-center\",\n                                                children: \"Testosterone Trajectory\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-20 flex items-end justify-center space-x-2 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-8 bg-white bg-opacity-60 rounded-t mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs\",\n                                                                children: \"16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-12 bg-white bg-opacity-70 rounded-t mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs\",\n                                                                children: \"17\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-16 bg-white bg-opacity-80 rounded-t mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs\",\n                                                                children: \"18\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-14 bg-white bg-opacity-90 rounded-t mb-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs\",\n                                                                children: \"19\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-20 bg-teal-200 rounded-t mb-1 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-bold\",\n                                                                children: \"20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-semibold bg-white bg-opacity-20 px-2 py-1 rounded\",\n                                                    children: \"\\uD83D\\uDCC8 Rising Trend\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 right-0 w-24 h-24 bg-white bg-opacity-10 rounded-full -mr-12 -mb-12\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex flex-col items-center w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-3\",\n                                                children: \"\\uD83D\\uDC80\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-3 text-center\",\n                                                children: \"Facial Bone Maturity\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative w-20 h-20 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-20 h-20 transform -rotate-90\",\n                                                        viewBox: \"0 0 36 36\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                className: \"text-white text-opacity-20\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"3\",\n                                                                fill: \"none\",\n                                                                d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                className: \"text-teal-200\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"3\",\n                                                                strokeDasharray: \"50, 100\",\n                                                                strokeLinecap: \"round\",\n                                                                fill: \"none\",\n                                                                d: \"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-bold\",\n                                                            children: \"50%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-semibold\",\n                                                        children: \"Still Growing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1 bg-white bg-opacity-20 px-2 py-1 rounded\",\n                                                        children: \"Peak at 25\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl shadow-xl p-6 text-white flex flex-col items-center relative overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-0 right-0 w-32 h-8 bg-white bg-opacity-10 rounded-full -mr-16 -mt-4 transform rotate-45\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative z-10 flex flex-col items-center w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-4xl mb-3\",\n                                                children: \"⏳\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-bold mb-3 text-center\",\n                                                children: \"Puberty Stage\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Early\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Peak\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Late\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-3 bg-white bg-opacity-20 rounded-full overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-3 bg-gradient-to-r from-white to-teal-200 rounded-full\",\n                                                            style: {\n                                                                width: '80%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between w-full mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-white rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-white rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-white rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-teal-200 rounded-full animate-ping\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 bg-white bg-opacity-40 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-semibold\",\n                                                        children: \"Late Stage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-1\",\n                                                        children: \"80% Complete\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8 text-white text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    children: \"\\uD83C\\uDFAF Your Genetic Potential Score\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-teal-200\",\n                                                    children: \"8.7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Overall Score\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl opacity-20\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-teal-200\",\n                                                    children: \"92%\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Accuracy\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl opacity-20\",\n                                            children: \"|\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-3xl font-bold text-teal-200\",\n                                                    children: \"2.3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: \"Years Left\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-6 md:px-20\",\n                id: \"pricing\",\n                style: {\n                    backgroundColor: '#141735'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    style: {\n                                        color: '#00C2A8'\n                                    },\n                                    children: \"Simple, No BS Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 max-w-3xl mx-auto\",\n                                    children: \"Choose the plan that fits your needs. No hidden fees, no subscriptions, just transparent pricing.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-2xl p-8 flex flex-col relative overflow-hidden hover:shadow-3xl transition-all duration-300 hover:-translate-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold mb-2 text-gray-800\",\n                                                    children: \"Essential Report\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-[#00C2A8]\",\n                                                            children: \"$1.00\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 ml-2\",\n                                                            children: \"one-time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Perfect for getting started with your genetic analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Instant growth & puberty analysis\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Basic genetic potential assessment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"PDF report delivered to email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Basic recommendations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] text-white py-4 rounded-xl font-bold text-lg hover:from-teal-500 hover:to-blue-900 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-2xl p-8 flex flex-col relative overflow-hidden hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 border-2 border-[#00C2A8]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-4 right-6 bg-[#00C2A8] text-white px-4 py-2 rounded-full text-sm font-bold\",\n                                            children: \"MOST POPULAR\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold mb-2 text-gray-800\",\n                                                    children: \"Maximizing Potential Blueprint\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-[#0A0E3F]\",\n                                                            children: \"$4.99\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 ml-2\",\n                                                            children: \"one-time\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Complete optimization guide for maximum results\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Everything in Essential Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Advanced looksmax optimization tips\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Detailed puberty phase timeline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Projected final height & features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 597,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Personalized action plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[#00C2A8] mr-3 mt-0.5 flex-shrink-0\",\n                                                                fill: \"currentColor\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 603,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: \"Priority email support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] text-white py-4 rounded-xl font-bold text-lg hover:from-blue-900 hover:to-teal-500 transition-all duration-300 shadow-lg hover:shadow-xl\",\n                                            children: \"Get Complete Analysis\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-[#00C2A8] mr-2\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Secure Payment Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-[#00C2A8] mr-2\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"30-Day Money Back Guarantee\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 629,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6 text-[#00C2A8] mr-2\",\n                                                    fill: \"currentColor\",\n                                                    viewBox: \"0 0 20 20\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fillRule: \"evenodd\",\n                                                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                                                        clipRule: \"evenodd\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"No Hidden Fees\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"https://cdn.jsdelivr.net/gh/stripe/stripe-js@v1.32.0/dist/stripe-logo.svg\",\n                                        alt: \"Powered by Stripe\",\n                                        className: \"h-8 opacity-60\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-6 md:px-20\",\n                id: \"faq\",\n                style: {\n                    backgroundColor: '#141735'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                    style: {\n                                        color: '#00C2A8'\n                                    },\n                                    children: \"Frequently Asked Questions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 650,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Get answers to common questions about our genetic analysis platform\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: faqs.map((faq, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-left p-8 focus:outline-none flex justify-between items-center hover:bg-gray-50 transition-colors duration-200\",\n                                            onClick: ()=>setOpenFaq(openFaq === idx ? null : idx),\n                                            \"aria-expanded\": openFaq === idx,\n                                            \"aria-controls\": `faq-answer-${idx}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-[#00C2A8] to-[#0A0E3F] rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-6 h-6 text-white\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl font-bold text-gray-800\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `ml-4 transition-all duration-300 ${openFaq === idx ? 'rotate-180 text-[#00C2A8]' : 'text-gray-400'}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this),\n                                        openFaq === idx && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            id: `faq-answer-${idx}`,\n                                            className: \"px-8 pb-8 animate-fade-in\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pl-16 pr-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-r from-[#00C2A8] to-[#0A0E3F] h-0.5 w-full mb-4 opacity-30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 680,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-700 text-lg leading-relaxed\",\n                                                        children: faq.answer\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, idx, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 bg-gradient-to-r from-[#0A0E3F] to-[#00C2A8] rounded-2xl p-8 text-center text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"Need Help?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg mb-6 opacity-90\",\n                                    children: \"Our team of genetic analysis experts is here to help you understand your results\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg mb-4\",\n                                    children: [\n                                        \"Email support: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            className: \"text-white underline hover:text-gray-200 transition-colors duration-300 font-semibold\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 56\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm opacity-75\",\n                                    children: \"Average response time: 2 hours\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 647,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-[#0A0E3F] text-white py-8 px-6 md:px-20 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-center max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg mb-4 md:mb-0\",\n                            children: \"BioAscension\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-6 text-sm mb-4 md:mb-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"hover:text-teal-400\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#how\",\n                                    className: \"hover:text-teal-400\",\n                                    children: \"How It Works\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#pricing\",\n                                    className: \"hover:text-teal-400\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#faq\",\n                                    className: \"hover:text-teal-400\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#contact\",\n                                    className: \"hover:text-teal-400\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-300\",\n                            children: \"\\xa9 2024 BioAscension. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                    lineNumber: 705,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n                lineNumber: 704,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/projects/bioascension/bioascension/pages/index.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%2Findex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();